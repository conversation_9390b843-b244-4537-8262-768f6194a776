export const IPC_CHANNELS = {
  tasks: {
    liveControl: {
      connect: 'tasks:liveControl:connect',
      disconnect: 'tasks:liveControl:disconnect',
      disconnectedEvent: 'tasks:liveControl:disconnectedEvent',
    },
    autoMessage: {
      start: 'tasks:autoMessage:start',
      stop: 'tasks:autoMessage:stop',
      stoppedEvent: 'tasks:autoMessage:stoppedEvent',
      updateConfig: 'tasks:autoMessage:updateConfig',
      sendBatchMessages: 'tasks:autoMessage:sendBatchMessages',
    },
    autoPopUp: {
      start: 'tasks:autoPopUp:start',
      stop: 'tasks:autoPopUp:stop',
      updateConfig: 'tasks:autoPopUp:updateConfig',
      stoppedEvent: 'tasks:autoPopUp:stoppedEvent',
      registerShortcuts: 'tasks:autoPopup:registerShortuct',
      unregisterShortcuts: 'tasks:autoPopup:unregisterShortcut',
    },
    aiChat: {
      chat: 'tasks:aiChat:chat',
      stream: 'tasks:aiChat:stream',
      error: 'tasks:aiChat:error',
      normalChat: 'tasks:aiChat:normalChat',
      testApiKey: 'tasks:aiChat:testApiKey',
      fetchModels: 'tasks:aiChat:fetchModels',
    },
    autoReply: {
      startCommentListener: 'tasks:autoReply:startCommentListener',
      stopCommentListener: 'tasks:autoReply:stopCommentListener',
      listenerStopped: 'tasks:autoReply:listenerStopped',
      showComment: 'tasks:autoReply:showComment',
      startAutoReply: 'tasks:autoReply:startAutoReply',
      stopAutoReply: 'tasks:autoReply:stopAutoReply',
      replyGenerated: 'tasks:autoReply:replyGenerated',
      sendReply: 'tasks:autoReply:sendReply',
    },
    autoVoice: {
      stoppedEvent: 'tasks:autoVoice:stoppedEvent',
      start: 'tasks:autoVoice:start',
      stop: 'tasks:autoVoice:stop',
      speakText: 'tasks:autoVoice:speakText',
    },
    autoTimeAnnouncement: {
      start: 'tasks:autoTimeAnnouncement:start',
      stop: 'tasks:autoTimeAnnouncement:stop',
      stoppedEvent: 'tasks:autoTimeAnnouncement:stoppedEvent',
      updateConfig: 'tasks:autoTimeAnnouncement:updateConfig',
    },
  },
  config: {
    save: 'config:save',
    load: 'config:load',
  },
  chrome: {
    getPath: 'chrome:getPath',
    setPath: 'chrome:setPath',
    selectPath: 'chrome:selectPath',
    toggleDevTools: 'chrome:toggleDevTools',
    saveState: 'chrome:saveState',
  },
  updater: {
    checkUpdate: 'updater:checkUpdate',
    updateAvailable: 'updater:updateAvailable',
    startDownload: 'updater:startDownload',
    downloadProgress: 'updater:downloadProgress',
    updateError: 'updater:updateError',
    updateDownloaded: 'updater:updateDownloaded',
    quitAndInstall: 'updater:quitAndInstall',
  },
  account: {
    switch: 'account:switch',
  },
  log: 'log',
  sendLog: 'sendLog', // 从渲染进程发送日志到主进程
  app: {
    openLogFolder: 'app:openLogFolder',
    notifyUpdate: 'app:notifyUpdate',
  },
  autoVoice: {
    addTimeAnnouncement: 'autoVoice:addTimeAnnouncement',
    addPassiveInteraction: 'autoVoice:addPassiveInteraction',
    addCommentReply: 'autoVoice:addCommentReply',
  },
  audioService: {
    selectScriptPath: 'audioService:selectScriptPath',
    start: 'audioService:start',
    stop: 'audioService:stop',
    status: 'audioService:status',
  },
  digitalHumanService: {
    selectScriptPath: 'digitalHumanService:selectScriptPath',
    start: 'digitalHumanService:start',
    stop: 'digitalHumanService:stop',
    status: 'digitalHumanService:status',
  },
  obsPlayer: {
    open: 'obsPlayer:open',
    close: 'obsPlayer:close',
    playVideo: 'obsPlayer:playVideo',
    videoEnded: 'obsPlayer:videoEnded',
    setAspectRatio: 'obsPlayer:setAspectRatio',
    // 双轨播放器支持
    openHost: 'obsPlayer:openHost',
    openAssistant: 'obsPlayer:openAssistant',
    closeHost: 'obsPlayer:closeHost',
    closeAssistant: 'obsPlayer:closeAssistant',
    playVideoHost: 'obsPlayer:playVideoHost',
    playVideoAssistant: 'obsPlayer:playVideoAssistant',
    videoEndedHost: 'obsPlayer:videoEndedHost',
    videoEndedAssistant: 'obsPlayer:videoEndedAssistant',
  },
  video: {
    cutSegment: 'video:cutSegment',
    checkFFmpeg: 'video:checkFFmpeg',
    getInfo: 'video:getInfo',
    concatenateVideos: 'video:concatenateVideos',
    writeFile: 'video:writeFile',
    setComfyUIPath: 'video:setComfyUIPath',
    separateAudio: 'video:separateAudio',
    extractVideo: 'video:extractVideo',
  },
  dialog: {
    showSaveDialog: 'dialog:showSaveDialog',
  },
  fs: {
    copyFile: 'fs:copyFile',
    exists: 'fs:exists',
  },
  shell: {
    showItemInFolder: 'shell:showItemInFolder',
  },
  streamServer: {
    start: 'streamServer:start',
    stop: 'streamServer:stop',
    status: 'streamServer:status',
    startStream: 'streamServer:startStream',
    stopStream: 'streamServer:stopStream',
    streamStatus: 'streamServer:streamStatus',
    startTaskStream: 'streamServer:startTaskStream',
    stopTaskStream: 'streamServer:stopTaskStream',
    updateVoiceList: 'streamServer:updateVoiceList',
    itemStreamed: 'streamServer:itemStreamed', // 推流完成事件
  },
} as const
