import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { useAutoReply } from '@/hooks/useAutoReply'
import { useAutoVoice } from '@/hooks/useAutoVoice'
import { useToast } from '@/hooks/useToast'
import { useEffect, useState } from 'react'
import { ChevronDown, ChevronRight } from 'lucide-react'

const AIReplyConfig = () => {
  const { config, updateAIReplySettings, resetAIReplyPromptToDefault, updateContextByScriptList } = useAutoReply()
  const { getAllScriptLists, getCurrentScriptList, switchScriptList } = useAutoVoice()
  const { toast } = useToast()
  const aiReplyEnabled = config.comment.aiReply.enable
  const autoSend = config.comment.aiReply.autoSend
  const autoSendToVoice = config.comment.aiReply.autoSendToVoice
  const assistantEnabled = config.assistantEnabled

  // 获取话术列表相关数据
  const allScriptLists = getAllScriptLists()
  const currentScriptList = getCurrentScriptList()

  // 直接计算当前知识库内容，不使用 getCurrentContext 函数
  const getCurrentContextValue = () => {
    if (!currentScriptList?.id) return config.comment.aiReply.context || ''
    return config.comment.aiReply.contextByScriptList[currentScriptList.id] || config.comment.aiReply.context || ''
  }

  // 使用 state 来管理当前的知识库内容，确保切换话术列表时能正确更新
  const [currentContext, setCurrentContext] = useState(getCurrentContextValue())

  // 知识库折叠状态
  const [isKnowledgeBaseOpen, setIsKnowledgeBaseOpen] = useState(false)

  // 系统提示词折叠状态
  const [isSystemPromptOpen, setIsSystemPromptOpen] = useState(false)

  // 当话术列表切换时，更新知识库内容
  useEffect(() => {
    const context = getCurrentContextValue()
    setCurrentContext(context)
  }, [currentScriptList?.id, config.comment.aiReply.contextByScriptList])



  // 处理AI自动回复开关
  const handleAiReplyChange = (checked: boolean) => {
    updateAIReplySettings({ enable: checked })
  }

  const handleAutoSendChange = (checked: boolean) => {
    updateAIReplySettings({ autoSend: checked })
  }

  const handleAutoSendToVoiceChange = (checked: boolean) => {
    updateAIReplySettings({ autoSendToVoice: checked })
  }

  const handleRestoreDefault = () => {
    resetAIReplyPromptToDefault()
    toast.success('已恢复为默认提示词')
  }

  // 处理知识库信息更新
  const handleContextChange = (value: string) => {
    setCurrentContext(value) // 立即更新本地状态
    if (currentScriptList) {
      updateContextByScriptList(currentScriptList.id, value)
    }
  }

  // 处理话术列表切换
  const handleScriptListChange = (listId: string) => {
    switchScriptList(listId)
    const list = allScriptLists.find(l => l.id === listId)
    if (list) {
      toast.success(`已切换到话术列表"${list.name}"`)
    }
  }

  return (
    <>
      {!assistantEnabled && (
        <div className="bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-3 mb-4">
          <p className="text-sm text-yellow-800 dark:text-yellow-200">
            ⚠️ 助理功能未开启，AI回复功能将不会生效。请先在<strong>基础设置</strong>中开启助理功能。
          </p>
        </div>
      )}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center space-x-2">
          <Switch
            id="ai-reply"
            checked={aiReplyEnabled}
            onCheckedChange={handleAiReplyChange}
            disabled={!assistantEnabled}
          />
          <Label htmlFor="ai-reply" className={!assistantEnabled ? 'text-muted-foreground' : ''}>
            启用AI回复
          </Label>
          {!assistantEnabled && (
            <span className="text-xs text-muted-foreground">(需要先开启助理功能)</span>
          )}
        </div>
        <div>
          <div className="flex items-center space-x-2">
            <Switch
              id="auto-send"
              checked={autoSend}
              onCheckedChange={handleAutoSendChange}
            />
            <Label htmlFor="auto-send">自动发送到直播间评论区</Label>
          </div>
          <div className="flex items-center space-x-2 mt-4">
            <Switch
              id="auto-send-to-voice"
              checked={autoSendToVoice}
              onCheckedChange={handleAutoSendToVoiceChange}
            />
            <Label htmlFor="auto-send-to-voice">自动发送到语音输出列表</Label>
          </div>
          <div className="text-xs text-muted-foreground mt-2 pl-2">
            <p>
              请注意：开启自动发送后，AI生成的所有回复都会自动发送到直播间，这可能会带来以下
              <strong>风险</strong>：
            </p>
            <ul className="list-disc pl-6 mt-1">
              <li>
                AI可能会生成<strong>不恰当或不相关</strong>的回复
              </li>
              <li>
                回复内容可能会<strong>违反平台规则</strong>
              </li>
              <li>可能会影响与观众的真实互动体验</li>
            </ul>
            <p className="font-medium mt-1">
              ※
              建议在开启自动发送前，先观察一段时间AI的回复质量。您也可以通过点击每条回复预览旁边的
              <strong>小飞机按钮</strong>来手动发送。
            </p>
          </div>
        </div>
      </div>
      {aiReplyEnabled && (
        <div className="space-y-4">
          <div className="space-y-4">
            <Collapsible open={isSystemPromptOpen} onOpenChange={setIsSystemPromptOpen}>
              <CollapsibleTrigger className="flex items-center justify-between w-full mb-2 hover:bg-gray-50 p-2 rounded-md transition-colors">
                <div className="flex items-center gap-2">
                  {isSystemPromptOpen ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                  <Label className="text-sm font-medium cursor-pointer">系统提示词</Label>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleRestoreDefault()
                  }}
                >
                  恢复默认
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-2">
                <Textarea
                  placeholder="输入AI角色提示词，定义AI的回复风格和行为..."
                  value={config.comment.aiReply.prompt}
                  onChange={e => updateAIReplySettings({ prompt: e.target.value })}
                  className="resize-none"
                  rows={8}
                />
                <p className="text-xs text-muted-foreground">
                  定义AI的角色、语气风格和回复规则。建议使用抖音直播间真人主播的语气风格。
                </p>
              </CollapsibleContent>
            </Collapsible>

            <Collapsible open={isKnowledgeBaseOpen} onOpenChange={setIsKnowledgeBaseOpen}>
              <CollapsibleTrigger className="flex items-center justify-between w-full mb-2 hover:bg-gray-50 p-2 rounded-md transition-colors">
                <div className="flex items-center gap-2">
                  {isKnowledgeBaseOpen ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                  <Label className="text-sm font-medium cursor-pointer">直播间知识库信息</Label>
                </div>
                <div className="flex items-center gap-2">
                  <Label className="text-xs text-muted-foreground">绑定话术:</Label>
                  <Select value={currentScriptList?.id || ''} onValueChange={handleScriptListChange}>
                    <SelectTrigger className="w-[180px] h-7 text-xs">
                      <SelectValue placeholder="选择话术列表" />
                    </SelectTrigger>
                    <SelectContent>
                      {allScriptLists.map((list) => (
                        <SelectItem key={list.id} value={list.id}>
                          <div className="flex items-center justify-between w-full">
                            <span>{list.name}</span>
                            <span className="text-xs text-muted-foreground ml-2">
                              ({list.scripts.length})
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-2">
                <Textarea
                  placeholder="输入直播间的背景信息，如直播类型、商品信息、观众群体等..."
                  value={currentContext || ''}
                  onChange={e => handleContextChange(e.target.value)}
                  className="resize-none"
                  rows={12}
                />
                <p className="text-xs text-muted-foreground">
                  知识库信息会跟随当前选中的话术列表。切换话术列表时，会自动加载对应的知识库信息。
                </p>
              </CollapsibleContent>
            </Collapsible>
          </div>
        </div>
      )}
    </>
  )
}

export default AIReplyConfig
