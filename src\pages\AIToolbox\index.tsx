import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { CarbonVideoPlayer } from '@/components/icons/carbon'
import { Scissors, Music } from 'lucide-react'
import VideoCutDialog from './components/VideoCutDialog'
import AudioVideoSeparationDialog from './components/AudioVideoSeparationDialog'

export default function AIToolbox() {
  const [isVideoCutDialogOpen, setIsVideoCutDialogOpen] = useState(false)
  const [isAudioVideoSeparationDialogOpen, setIsAudioVideoSeparationDialogOpen] = useState(false)

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">AI工具箱</h1>
        <p className="text-muted-foreground">
          集成各种AI辅助工具，提升工作效率
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* 视频切割工具 */}
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <CarbonVideoPlayer className="w-6 h-6 text-primary" />
              </div>
              <div>
                <CardTitle className="text-lg">视频切割工具</CardTitle>
                <CardDescription>
                  支持多种切割方式，快速分割视频文件
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="text-sm text-muted-foreground">
                • 支持拖拽上传视频文件<br />
                • 按时间段切割<br />
                • 按分钟自动切割<br />
                • 高质量输出
              </div>
              <Button
                onClick={() => setIsVideoCutDialogOpen(true)}
                className="w-full"
              >
                <Scissors className="w-4 h-4 mr-2" />
                开始切割
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 音画分离工具 */}
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Music className="w-6 h-6 text-primary" />
              </div>
              <div>
                <CardTitle className="text-lg">音画分离工具</CardTitle>
                <CardDescription>
                  分离视频中的音频和视频，支持多种格式
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="text-sm text-muted-foreground">
                • 支持拖拽上传视频文件<br />
                • 分离音频（MP3/WAV/AAC）<br />
                • 提取无音频视频<br />
                • 高质量输出
              </div>
              <Button
                onClick={() => setIsAudioVideoSeparationDialogOpen(true)}
                className="w-full"
              >
                <Music className="w-4 h-4 mr-2" />
                开始分离
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 视频切割弹窗 */}
      <VideoCutDialog
        open={isVideoCutDialogOpen}
        onOpenChange={setIsVideoCutDialogOpen}
      />

      {/* 音画分离弹窗 */}
      <AudioVideoSeparationDialog
        open={isAudioVideoSeparationDialogOpen}
        onOpenChange={setIsAudioVideoSeparationDialogOpen}
      />
    </div>
  )
}
